package player

import (
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/pkg/log"
)

// Item 道具模块
type Item struct {
	player *Player
}

func NewItem(p *Player) *Item {
	return &Item{
		player: p,
	}
}

// InitDB 初始化模块数据
func (i *Item) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	log.Info("Item InitDB")
}

// OnCrossDay 处理跨天逻辑
func (i *Item) OnCrossDay(natural bool, nowUnix int64) {
	// 处理跨天逻辑，如清理过期道具等
	// TODO: 实现跨天逻辑
}

// IsMoneyType 通用接口：判断道具类型
// @Export
func (i *Item) IsMoneyType(itemId int32) bool {
	if itemId <= 0 {
		log.Error("[Item.IsMoneyType] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId))
		return false
	}
	return i.player.IsMoneyType(itemId)
}

// IsCanDelItem 通用接口：检查是否可以扣除道具
// @Export
func (i *Item) IsCanDelItem(itemId int32, delMoneyNum int32) bool {
	if itemId <= 0 {
		log.Error("[Player.IsCanDelItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("delMoneyNum", delMoneyNum))
		return false
	}
	if i.IsMoneyType(itemId) {
		return i.player.IsCanDelMoney(itemId, delMoneyNum)
	} else {
		return i.player.IsBagCanDelItem(itemId, delMoneyNum)
	}
}

// GetItemNumById 通用接口：根据货币 Id 获取数量
// @Export
func (i *Item) GetItemNumById(itemId int32) int32 {
	if itemId <= 0 {
		log.Error("[Player.GetItemNumById] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId))
		return -1
	}
	if i.IsMoneyType(itemId) {
		return i.player.GetMoneyNumById(itemId)
	} else {
		return i.player.GetBagItemNumById(itemId)
	}
}

// AddItem 通用接口：添加道具
// @Export
func (i *Item) AddItem(itemId int32, addItemNum int32, operateReason int32) bool {
	if itemId <= 0 || addItemNum <= 0 {
		log.Error("[Player.AddItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("addItemNum", addItemNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(itemId)
	if itemInfo == nil {
		log.Error("[Player.AddItem] item id is illegal", log.Kv("itemId", itemId))
		return false
	}
	addResult := true
	if i.IsMoneyType(itemId) {
		addResult = i.player.AddMoney(itemId, addItemNum, operateReason)
	} else {
		addResult = i.player.AddBagItem(itemId, addItemNum, operateReason)
	}
	// 记录道具BI日志
	i.player.BIItemLog(itemInfo.Type, itemId, addItemNum, operateReason, 1, i.player.GetItemNumById(itemId))
	return addResult
}

// DelItem 通用接口：删除道具
// @Export
func (i *Item) DelItem(itemId int32, delItemNum int32, operateReason int32) bool {
	if itemId <= 0 || delItemNum <= 0 {
		log.Error("[Player.DelItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("delItemNum", delItemNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(itemId)
	if itemInfo == nil {
		log.Error("[Player.DelItem] item id is illegal", log.Kv("itemId", itemId))
		return false
	}
	addResult := true
	if i.IsMoneyType(itemId) {
		addResult = i.player.DelMoney(itemId, delItemNum, operateReason)
	} else {
		addResult = i.player.DelBagItem(itemId, delItemNum, operateReason)
	}
	// 记录道具BI日志
	i.player.BIItemLog(itemInfo.Type, itemId, delItemNum, operateReason, -1, i.player.GetItemNumById(itemId))
	return addResult
}

// DropItemToPlayer 通用接口：掉落专用接口
// @Export
func (i *Item) DropItemToPlayer(itemId int32, addItemNum int32, operateReason int32) bool {
	if itemId <= 0 || addItemNum <= 0 {
		log.Error("[Player.DropItemToPlayer] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", itemId),
			log.Kv("addItemNum", addItemNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(itemId)
	if itemInfo == nil {
		log.Error("[Player.DropItemToPlayer] item id is illegal", log.Kv("itemId", itemId))
		return false
	}
	return i.AddItem(itemId, addItemNum, operateReason)
}

// GetDropItemByDropID 通用接口：掉落道具接口
// @Export
func (i *Item) GetDropItemByDropID(dropId int32, operateReason int32) map[int32]int32 {
	resultItems := make(map[int32]int32)
	if dropId <= 0 {
		log.Error("[Player.GetDropItemByDropGroupID] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("dropId", dropId),
			log.Kv("operateReason", operateReason))
		return resultItems
	}
	resultItems = i.player.GenDropItems(dropId)
	for itemId, itemNum := range resultItems {
		i.AddItem(itemId, itemNum, operateReason)
	}
	i.SendCommonDropItemList(resultItems, operateReason)
	return resultItems
}

// GetDropItemByDropGroupID 通用接口：掉落道具接口
// @Export
func (i *Item) GetDropItemByDropGroupID(dropGroupId int32, operateReason int32) map[int32]int32 {
	resultItems := make(map[int32]int32)
	if dropGroupId <= 0 {
		log.Error("[Player.GetDropItemByDropGroupID] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("dropGroupId", dropGroupId),
			log.Kv("operateReason", operateReason))
		return resultItems
	}
	resultItems = i.player.GetDropItemsByGroupId(dropGroupId)
	for itemId, itemNum := range resultItems {
		i.AddItem(itemId, itemNum, operateReason)
	}
	i.SendCommonDropItemList(resultItems, operateReason)
	return resultItems
}

// SendCommonDropItemList 通用接口：发送掉落道具接口
// @Export
func (i *Item) SendCommonDropItemList(dropItemList map[int32]int32, operateReason int32) {
	// 构建最终消息
	msg := &cs.LCDropItemListRes{
		ItemInfo: []*public.PBDropItemDataInfo{},
		DropType: operateReason,
	}
	for dropItemId, dropItemNum := range dropItemList {
		msg.ItemInfo = append(msg.ItemInfo, &public.PBDropItemDataInfo{
			ItemId:    dropItemId,
			ItemCount: dropItemNum,
		})
	}
	// 将消息发送给客户端
	i.player.SendToClient(rpc_def.LCDropItemListRes, msg, false)
}

// UseItem 使用道具
// @Export
func (i *Item) UseItem(param *public.UseItemParam) error_code.Code {
	// 前置判断
	if param.ItemId <= 0 || param.UseNum <= 0 {
		log.Error("[Item.UseItem] invalid params",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	if i.player.IsBagFull() == true {
		log.Error("[Item.UseItem] bag is full",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	// 条件判断
	itemNum := i.player.GetBagItemNumById(param.ItemId)
	if itemNum < param.UseNum {
		log.Error("[Item.UseItem] item num is not enough",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	// 扣除道具
	isDelSuccess := i.player.DelBagItem(param.ItemId, param.UseNum, 1)
	if isDelSuccess != true {
		log.Error("[Item.UseItem] item del fail",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}
	// 执行掉落逻辑
	tb := table.GetTable()
	itemInfo := tb.TableItem.GetById(param.ItemId)
	if itemInfo == nil {
		log.Error("[Item.UseItem] item id is illegal",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("UseNum", param.UseNum))
		return error_code.ERROR_PARAMS
	}

	// 根据道具类型执行不同逻辑
	switch public.ItemType(itemInfo.Type) {
	case public.ItemType_Drop:
		// 普通掉落道具逻辑
		return i.handleDropItem(itemInfo, param)
	case public.ItemType_ChooseBox:
		// 自选宝箱逻辑
		return i.handleChooseBoxItem(itemInfo, param)
	default:
		log.Error("[Item.UseItem] unsupported item type",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("itemType", itemInfo.Type))
		return error_code.ERROR_PARAMS
	}
}

// handleDropItem 处理普通掉落道具
func (i *Item) handleDropItem(itemInfo *table_data.TableItem, param *public.UseItemParam) error_code.Code {
	if len(itemInfo.NumParams) == 0 {
		log.Error("[Item.handleDropItem] item NumParams is empty",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId))
		return error_code.ERROR_PARAMS
	}

	itemDropId := itemInfo.NumParams[0]
	if itemDropId <= 0 {
		log.Error("[Item.handleDropItem] item drop id is illegal",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("dropId", itemDropId))
		return error_code.ERROR_PARAMS
	}

	// 执行掉落逻辑
	for useCount := int32(0); useCount < param.UseNum; useCount++ {
		i.GetDropItemByDropID(itemDropId, 4)
	}

	return error_code.ERROR_OK
}

// handleChooseBoxItem 处理自选宝箱道具
func (i *Item) handleChooseBoxItem(itemInfo *table_data.TableItem, param *public.UseItemParam) error_code.Code {
	// 自选宝箱一次只能使用一个
	if param.UseNum != 1 {
		log.Error("[Item.handleChooseBoxItem] choose box can only use one at a time",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("useNum", param.UseNum))
		return error_code.ERROR_CHOOSE_BOX_SINGLE_USE
	}

	// 验证chooseInfos参数
	if len(param.ChooseInfos) == 0 {
		log.Error("[Item.handleChooseBoxItem] chooseInfos is empty",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId))
		return error_code.ERROR_CHOOSE_BOX_CONFIG_ERROR
	}

	// 获取自选宝箱配置
	if len(itemInfo.NumParams) == 0 {
		log.Error("[Item.handleChooseBoxItem] item NumParams is empty",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId))
		return error_code.ERROR_CHOOSE_BOX_CONFIG_ERROR
	}

	dropId := itemInfo.NumParams[0]
	tb := table.GetTable()
	dropInfo := tb.TableDropBox.GetById(dropId)
	if dropInfo == nil {
		log.Error("[Item.handleChooseBoxItem] drop config not found",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("dropId", dropId))
		return error_code.ERROR_CHOOSE_BOX_CONFIG_ERROR
	}

	// 获取可选道具列表（从drop系统中获取）
	validChoices := i.player.Drop().GetValidChoicesForDropBox(dropId)

	// 获取自选宝箱配置中所有道具的总数量限制
	totalConfigCount := i.player.Drop().GetTotalItemCountForChooseBox(dropId)
	if totalConfigCount <= 0 {
		log.Error("[Item.handleChooseBoxItem] no valid items in choose box config",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("dropId", dropId))
		return error_code.ERROR_CHOOSE_BOX_CONFIG_ERROR
	}

	// 计算总选择次数和验证选择
	totalChooseCount := int32(0)
	for _, chooseInfo := range param.ChooseInfos {
		if chooseInfo.Value <= 0 {
			log.Error("[Item.handleChooseBoxItem] invalid choose count",
				log.Kv("uid", i.player.Uid()),
				log.Kv("itemId", param.ItemId),
				log.Kv("chooseItemId", chooseInfo.Key),
				log.Kv("chooseCount", chooseInfo.Value))
			return error_code.ERROR_CHOOSE_BOX_INVALID_COUNT
		}

		if !validChoices[chooseInfo.Key] {
			log.Error("[Item.handleChooseBoxItem] invalid choose item",
				log.Kv("uid", i.player.Uid()),
				log.Kv("itemId", param.ItemId),
				log.Kv("chooseItemId", chooseInfo.Key))
			return error_code.ERROR_CHOOSE_BOX_INVALID_ITEM
		}

		totalChooseCount += chooseInfo.Value
	}

	// 检查选择的总数量是否超过配置中道具的总数量限制
	// 自选宝箱一次使用，选择的道具总数不能超过DropItem1配置中所有道具的总数量
	if totalChooseCount > totalConfigCount {
		log.Error("[Item.handleChooseBoxItem] choose count exceeds config limit",
			log.Kv("uid", i.player.Uid()),
			log.Kv("itemId", param.ItemId),
			log.Kv("totalConfigCount", totalConfigCount),
			log.Kv("totalChooseCount", totalChooseCount))
		return error_code.ERROR_CHOOSE_BOX_COUNT_EXCEED
	}

	// 发放选择的道具
	dropItemList := make(map[int32]int32)
	for _, chooseInfo := range param.ChooseInfos {
		i.AddItem(chooseInfo.Key, chooseInfo.Value, 4)
		dropItemList[chooseInfo.Key] = chooseInfo.Value
	}

	// 发送掉落物品列表给客户端
	if len(dropItemList) > 0 {
		i.SendCommonDropItemList(dropItemList, 4)
	}

	log.Info("[Item.handleChooseBoxItem] choose box used successfully",
		log.Kv("uid", i.player.Uid()),
		log.Kv("itemId", param.ItemId),
		log.Kv("useNum", param.UseNum),
		log.Kv("chooseCount", len(param.ChooseInfos)))

	return error_code.ERROR_OK
}
