package player

import (
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/common/table"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/pkg/log"
	"time"
)

// Money 货币模块
type Money struct {
	player    *Player
	Moneys    map[int32]int32
	powerData *PowerData // 玩家体力相关数据
}

type PowerData struct {
	PowerRemains        *dbstruct.PowerBuyData          // 体力购买次数
	lastPowerRewardTime int64                           // 玩家上次生成体力包的时间
	powerRewards        []*public.PBPowerRewardDataInfo // 玩家可领取的体力包列表
}

func NewMoney(p *Player) *Money {
	return &Money{
		player: p,
		Moneys: make(map[int32]int32),
		powerData: &PowerData{
			PowerRemains:        &dbstruct.PowerBuyData{},
			lastPowerRewardTime: 0,
			powerRewards:        make([]*public.PBPowerRewardDataInfo, 0),
		},
	}
}

// InitDB 初始化模块数据
func (m *Money) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Money == nil {
		db.Game.Money = &dbstruct.Money{
			Moneys: make([]*dbstruct.MoneyData, 0),
		}
	}
	m.Moneys = make(map[int32]int32)
	for _, money := range db.Game.Money.Moneys {
		m.Moneys[money.MoneyId] = int32(money.MoneyNum)
	}
	if db.Game.Money.Power == nil {
		db.Game.Money.Power = &dbstruct.PowerData{}
	}
	// 体力购买次数
	if db.Game.Money.Power.PowerRemains == nil {
		missRemains := int32(0)
		if table.GetTable().TableEnergyBuy.GetById(1).StorageTimes >
			passDaySinceCreated(m.player.db.Base.CreateTime)*
				table.GetTable().TableEnergyBuy.GetById(1).Times {
			missRemains = passDaySinceCreated(m.player.db.Base.CreateTime) *
				table.GetTable().TableEnergyBuy.GetById(1).Times
		} else {
			missRemains = table.GetTable().TableEnergyBuy.GetById(1).StorageTimes
		}
		db.Game.Money.Power.PowerRemains = &dbstruct.PowerBuyData{
			NormalRemains: table.GetTable().TableEnergyBuy.GetById(1).Times,
			MissRemains:   missRemains,
			VideoRemains:  table.GetTable().TableEnergyBuy.GetById(2).Times,
		}
	}
	m.powerData.PowerRemains = db.Game.Money.Power.PowerRemains
	// 体力领取包
	if db.Game.Money.Power.PowerRewards == nil {
		db.Game.Money.Power.PowerRewards = []*public.PBPowerRewardDataInfo{}
	}
	m.powerData.powerRewards = db.Game.Money.Power.PowerRewards
	if db.Game.Money.Power.LastPowerRewardTime <= 0 {
		db.Game.Money.Power.LastPowerRewardTime = db.GetBase().CreateTime
	}
	m.powerData.lastPowerRewardTime = db.Game.Money.Power.LastPowerRewardTime
	log.Info("Money InitDB")
}

// SaveDB 存储模块数据
func (m *Money) SaveDB() {
	// 清空旧的存储数据
	m.player.GetUserDB().Game.Money.Moneys = make([]*dbstruct.MoneyData, 0)

	// 遍历 Moneys，将其写入存储结构
	for moneyId, moneyNum := range m.Moneys {
		m.player.GetUserDB().Game.Money.Moneys = append(m.player.GetUserDB().Game.Money.Moneys,
			&dbstruct.MoneyData{
				MoneyId:  moneyId,
				MoneyNum: int64(moneyNum),
			})
	}
	// 体力可购买次数
	m.player.GetUserDB().Game.Money.Power.PowerRemains = m.powerData.PowerRemains
	// 体力包
	// m.player.db.Game.Money.Power.LastPowerRewardTime = m.powerData.lastPowerRewardTime
	// m.player.db.Game.Money.Power.PowerRewards = m.powerData.powerRewards
	log.Info("Money SaveDB")
}

// OnCrossDay 处理跨天逻辑
func (m *Money) OnCrossDay(natural bool, nowUnix int64) {
	// 处理跨天逻辑，如清理过期道具等
	// TODO: 实现跨天逻辑

	// 跨天处理体力购买次数，把错过购买的次数累积起来
	newMissing := m.powerData.PowerRemains.MissRemains + m.powerData.PowerRemains.NormalRemains
	m.powerData.PowerRemains.NormalRemains = table.GetTable().TableEnergyBuy.GetById(1).Times
	if newMissing > table.GetTable().TableEnergyBuy.GetById(1).StorageTimes {
		m.powerData.PowerRemains.MissRemains = table.GetTable().TableEnergyBuy.GetById(1).StorageTimes
	} else {
		m.powerData.PowerRemains.MissRemains = newMissing
	}
	m.powerData.PowerRemains.VideoRemains = table.GetTable().TableEnergyBuy.GetById(2).Times
	m.player.GetUserDB().Game.Money.Power.PowerRemains = m.powerData.PowerRemains
	msg := &cs.LCOpenPowerBuyingRes{
		NormalRemains: m.powerData.PowerRemains.NormalRemains,
		MissRemains:   m.powerData.PowerRemains.MissRemains,
		VideoRemains:  m.powerData.PowerRemains.VideoRemains,
	}
	// 将消息发送给客户端
	m.player.SendToClient(rpc_def.LCOpenPowerBuyingRes, msg, false)
}

// AddMoney 添加货币
// @Export
func (m *Money) AddMoney(moneyId int32, addMoneyNum int32, operateReason int32) bool {
	if moneyId <= 0 || moneyId > 6 || addMoneyNum <= 0 {
		log.Error("[Money.AddMoney] invalid params",
			log.Kv("uid", m.player.Uid()),
			log.Kv("moneyId", moneyId),
			log.Kv("addMoneyNum", addMoneyNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	// 判断是否已有该 moneyId，若没有则初始化，若有则累加
	if currentNum, exists := m.Moneys[moneyId]; exists {
		currentNum += addMoneyNum
		m.Moneys[moneyId] = currentNum
	} else {
		m.Moneys[moneyId] = addMoneyNum
	}
	m.SaveDB()
	m.SyncAllMoneyData()
	log.Info("[Money.AddMoney] success",
		log.Kv("uid", m.player.Uid()),
		log.Kv("moneyId", moneyId),
		log.Kv("addMoneyNum", addMoneyNum),
		log.Kv("curTotalMoney", m.Moneys[moneyId]),
		log.Kv("operateReason", operateReason))
	return true
}

// DelMoney 删除货币
// @Export
func (m *Money) DelMoney(moneyId int32, delMoneyNum int32, operateReason int32) bool {
	if moneyId <= 0 || moneyId > 6 || delMoneyNum <= 0 {
		log.Error("[Money.DelMoney] invalid params",
			log.Kv("uid", m.player.Uid()),
			log.Kv("moneyId", moneyId),
			log.Kv("delMoneyNum", delMoneyNum),
			log.Kv("operateReason", operateReason))
		return false
	}
	// 检查是否有足够的货币可以扣除
	isDelSuccess := -1
	currentNum, exists := m.Moneys[moneyId]
	if exists {
		if currentNum >= delMoneyNum {
			currentNum -= delMoneyNum
			m.Moneys[moneyId] = currentNum
			isDelSuccess = 0
		}
	}
	m.SaveDB()
	m.SyncAllMoneyData()
	if isDelSuccess == 0 {
		log.Info("[Money.DelMoney] success",
			log.Kv("uid", m.player.Uid()),
			log.Kv("moneyId", moneyId),
			log.Kv("delMoneyNum", delMoneyNum),
			log.Kv("curTotalMoney", currentNum),
			log.Kv("operateReason", operateReason))
		return true
	} else {
		log.Error("[Money.DelMoney] operate fail",
			log.Kv("uid", m.player.Uid()),
			log.Kv("moneyId", moneyId))
		return false
	}
}

// IsMoneyType 检查是否是货币类型
// @Export
func (m *Money) IsMoneyType(moneyId int32) bool {
	if moneyId <= 0 || moneyId > 6 {
		return false
	}
	return true
}

// IsCanDelMoney 检查是否可以扣除货币
// @Export
func (m *Money) IsCanDelMoney(moneyId int32, delMoneyNum int32) bool {
	if moneyId <= 0 || moneyId > 6 || delMoneyNum <= 0 {
		return false
	}
	// 检查是否有该货币且数量足够
	if currentNum, exists := m.Moneys[moneyId]; exists && currentNum >= delMoneyNum {

		return true
	}
	return false
}

// GetMoneyNumById 根据货币 Id 获取数量
// @Export
func (m *Money) GetMoneyNumById(moneyId int32) int32 {
	if moneyId <= 0 || moneyId > 6 {
		log.Error("[Money.GetMoneyNumById] invalid params",
			log.Kv("uid", m.player.Uid()),
			log.Kv("moneyId", moneyId))
		return 0
	}
	// 如果存在返回货币数量，不存在该货币返回 0
	if currentNum, exists := m.Moneys[moneyId]; exists {
		return currentNum
	}
	return 0
}

// SyncAllMoneyData 同步全部货币数据到客户端
// @Export
func (m *Money) SyncAllMoneyData() {
	// 检查玩家是否在线
	if !m.player.IsOnline() {
		return
	}
	// 初始化 PBPlayerMoneyInfo
	playerMoneyInfo := &public.PBPlayerMoneyInfo{
		Gold:      0, // 金币
		Diamon:    0, // 钻石
		Power:     0, // 体力
		Refined:   0, // 洗炼石
		Hallows:   0, // 圣物币
		GuildCoin: 0, // 公会币
	}
	// 遍历 Moneys map，赋值给对应字段
	for moneyId, moneyNum := range m.Moneys {
		switch moneyId {
		case 1:
			playerMoneyInfo.Gold = int32(moneyNum)
		case 2:
			playerMoneyInfo.Diamon = int32(moneyNum)
		case 3:
			playerMoneyInfo.Power = int32(moneyNum)
		case 4:
			playerMoneyInfo.Refined = int32(moneyNum)
		case 5:
			playerMoneyInfo.Hallows = int32(moneyNum)
		case 6:
			playerMoneyInfo.GuildCoin = int32(moneyNum)
		default:
			log.Warn("[SyncAllMoneyData] Unknown moneyId",
				log.Kv("uid", m.player.Uid()),
				log.Kv("moneyId", moneyId))
		}
	}
	// 构建最终消息
	msg := &cs.LC_PlayerResource_Sync{
		MoneyData: playerMoneyInfo,
	}
	// 将消息发送给客户端
	m.player.SendToClient(rpc_def.LC_PlayerResource_Sync, msg, false)
	log.Info("SyncAllMoneyData sent successfully")
}

// SyncMoneyUpdInfo 同步单位货币信息到客户端
func (m *Money) SyncMoneyUpdInfo(moneyId int32) {

}

// 返回剩余可购买体力次数
func (m *Money) SyncPowerBuyTimes() (out *cs.LCOpenPowerBuyingRes) {
	// if m.powerData.PowerRemains == nil {
	// 	if m.player.db.Game.Money.Power.PowerRemains == nil {
	// 		missRemains := int32(0)
	// 		if table.GetTable().TableEnergyBuy.GetById(1).StorageTimes >
	// 			passDaySinceCreated(m.player.db.Base.CreateTime)*
	// 				table.GetTable().TableEnergyBuy.GetById(1).Times {
	// 			missRemains = passDaySinceCreated(m.player.db.Base.CreateTime) *
	// 				table.GetTable().TableEnergyBuy.GetById(1).Times
	// 		} else {
	// 			missRemains = table.GetTable().TableEnergyBuy.GetById(1).StorageTimes
	// 		}
	// 		m.player.db.Game.Money.Power.PowerRemains = &dbstruct.PowerBuyData{
	// 			NormalRemains: table.GetTable().TableEnergyBuy.GetById(1).Times,
	// 			MissRemains:   missRemains,
	// 			VideoRemains:  table.GetTable().TableEnergyBuy.GetById(2).Times,
	// 		}
	// 	}
	// 	m.powerData.PowerRemains = m.player.db.Game.Money.Power.PowerRemains
	// }
	return &cs.LCOpenPowerBuyingRes{
		NormalRemains: m.powerData.PowerRemains.NormalRemains,
		MissRemains:   m.powerData.PowerRemains.MissRemains,
		VideoRemains:  m.powerData.PowerRemains.VideoRemains,
	}
}

// 购买体力 扣减次数和钻石
func (m *Money) BuyPower(BuyType int32, BuyNum int32) (out *cs.LCPowerBuyingRes) {
	// 以防购买次数未初始化
	// if m.powerData.PowerRemains == nil {
	// 	if m.player.db.Game.Money.Power.PowerRemains == nil {
	// 		missRemains := int32(0)
	// 		if table.GetTable().TableEnergyBuy.GetById(1).StorageTimes >
	// 			passDaySinceCreated(m.player.db.Base.CreateTime)*
	// 				table.GetTable().TableEnergyBuy.GetById(1).Times {
	// 			missRemains = passDaySinceCreated(m.player.db.Base.CreateTime) *
	// 				table.GetTable().TableEnergyBuy.GetById(1).Times
	// 		} else {
	// 			missRemains = table.GetTable().TableEnergyBuy.GetById(1).StorageTimes
	// 		}
	// 		m.player.db.Game.Money.Power.PowerRemains = &dbstruct.PowerBuyData{
	// 			NormalRemains: table.GetTable().TableEnergyBuy.GetById(1).Times,
	// 			MissRemains:   missRemains,
	// 			VideoRemains:  table.GetTable().TableEnergyBuy.GetById(2).Times,
	// 		}
	// 	}
	// 	m.powerData.PowerRemains = m.player.db.Game.Money.Power.PowerRemains
	// }
	// 读配置找到一次买几个体力和消耗的货币
	result := &cs.LCPowerBuyingRes{
		Result: 1,
	}
	var powerNum, MoneyType, MoneyNum int32 = 0, 0, 0
	switch BuyType {
	case 0:
		if len(table.GetTable().TableEnergyBuy.GetById(1).Item) == 2 {
			powerNum = table.GetTable().TableEnergyBuy.GetById(1).Energy
			MoneyType = table.GetTable().TableEnergyBuy.GetById(1).Item[0]
			MoneyNum = table.GetTable().TableEnergyBuy.GetById(1).Item[1]
		}
		if m.powerData.PowerRemains.NormalRemains > 0 && m.DelMoney(MoneyType, MoneyNum, 1) {
			if m.AddMoney(game_def.NickPower, powerNum, 1) {
				result.Result = 0
				m.powerData.PowerRemains.NormalRemains--
				m.player.GetUserDB().Game.Money.Power.PowerRemains.NormalRemains =
					m.powerData.PowerRemains.NormalRemains
			}
		}
		break
	case 1:
		if len(table.GetTable().TableEnergyBuy.GetById(1).Item) == 2 {
			powerNum = table.GetTable().TableEnergyBuy.GetById(1).Energy * BuyNum
			MoneyType = table.GetTable().TableEnergyBuy.GetById(1).Item[0]
			MoneyNum = table.GetTable().TableEnergyBuy.GetById(1).Item[1] * BuyNum
		}
		if m.powerData.PowerRemains.MissRemains >= BuyNum && m.DelMoney(MoneyType, MoneyNum, 1) {
			if m.AddMoney(game_def.NickPower, powerNum, 1) {
				result.Result = 0
				m.powerData.PowerRemains.MissRemains = m.powerData.PowerRemains.MissRemains - BuyNum
				m.player.GetUserDB().Game.Money.Power.PowerRemains.MissRemains =
					m.powerData.PowerRemains.MissRemains
			}
		}
		break
	case 2:
		powerNum = table.GetTable().TableEnergyBuy.GetById(0).Energy
		if m.powerData.PowerRemains.VideoRemains > 0 && m.AddMoney(game_def.NickPower, powerNum, 1) {
			result.Result = 0
			m.powerData.PowerRemains.VideoRemains--
			m.player.GetUserDB().Game.Money.Power.PowerRemains.VideoRemains = m.powerData.PowerRemains.VideoRemains
		}
		break
	}
	result.NormalRemains = m.powerData.PowerRemains.NormalRemains
	result.MissRemains = m.powerData.PowerRemains.MissRemains
	result.VideoRemains = m.powerData.PowerRemains.VideoRemains
	return result
}

// 计算玩家从创建开始到今天之前经历过几天，用于累计错过体力购买次数
func passDaySinceCreated(createTime int64) int32 {
	createdAt := time.Unix(createTime, 0)
	now := time.Now()
	todayZero := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	// 如果玩家是今天创建的（零点之后），返回 0
	if createdAt.After(todayZero) {
		return 0
	}
	// 将创建时间归一化为“那天的零点”
	createdZero := time.Date(createdAt.Year(), createdAt.Month(), createdAt.Day(),
		0, 0, 0, 0, createdAt.Location())
	// 计算天数差
	duration := todayZero.Sub(createdZero)
	return int32(duration.Hours() / 24)
}

// 同步可领取体力列表
func (m *Money) SyncAllPowerRewardData() *cs.LCSyncPowerRewardList {
	m.RefreshPowerRewardsInRealTime()
	result := &cs.LCSyncPowerRewardList{
		LastPowerRewardTime: m.powerData.lastPowerRewardTime,
	}
	for _, reward := range m.powerData.powerRewards {
		if reward != nil {
			result.PowerRewardInfoList = append(result.PowerRewardInfoList, reward)
		}
	}
	return result
}

// 刷新当前时间体力包
func (m *Money) RefreshPowerRewardsInRealTime() {
	// 清除过期体力
	powerValidity := int64(table.GetPowerPeriodValidity() * 86400) // 体力有效期（秒）
	newRewards := make([]*public.PBPowerRewardDataInfo, 0)
	now := time.Now().Unix()
	for _, reward := range m.powerData.powerRewards {
		if reward.StartTime+powerValidity >= now {
			newRewards = append(newRewards, reward)
		}
	}
	m.powerData.powerRewards = newRewards
	// 从此刻开始向前生成补发体力包 直到上一生成的时间点或到达存储上限
	refreshHours := table.GetPowerRefreshTime() // 每天发礼包的时间点的int数组
	if len(refreshHours) == 0 {
		return
	}
	limit := table.GetPowerStorageLimit()
	if limit <= 0 {
		return
	}
	if m.powerData.powerRewards == nil {
		m.powerData.powerRewards = make([]*public.PBPowerRewardDataInfo, 0)
	}
	// 从当前时间开始，回溯每一天的刷新点
	current := time.Unix(now, 0)
	for len(m.powerData.powerRewards) < int(limit) {
		// 获取今天的零点时间
		zero := time.Date(current.Year(), current.Month(), current.Day(),
			0, 0, 0, 0, current.Location())
		// 遍历所有刷新时间点（倒序）
		for i := len(refreshHours) - 1; i >= 0; i-- {
			if len(m.powerData.powerRewards) >= int(limit) {
				m.powerData.lastPowerRewardTime = now
				m.player.GetUserDB().GetGame().Money.Power.LastPowerRewardTime = now
				m.player.db.Game.Money.Power.PowerRewards = m.powerData.powerRewards
				return // 达到上限，不再生成
			}
			refreshTime := zero.Add(time.Duration(refreshHours[i]) * time.Hour).Unix()
			if refreshTime <= m.powerData.lastPowerRewardTime {
				m.powerData.lastPowerRewardTime = now
				m.player.GetUserDB().GetGame().Money.Power.LastPowerRewardTime = now
				m.player.db.Game.Money.Power.PowerRewards = m.powerData.powerRewards
				return // 已到上一次生成的时间点 不再生成
			}
			if refreshTime <= now && refreshTime+powerValidity > now {
				reward := &public.PBPowerRewardDataInfo{
					StartTime:  refreshTime,
					PowerCount: table.GetPowerNum(),
				}
				m.powerData.powerRewards = append(m.powerData.powerRewards, reward)
			}
		}
		// 继续回退一天
		current = current.AddDate(0, 0, -1)
	}
	m.powerData.lastPowerRewardTime = now
	m.player.GetUserDB().GetGame().Money.Power.LastPowerRewardTime = now
	m.player.db.Game.Money.Power.PowerRewards = m.powerData.powerRewards
	return
}

// 一键领取全部体力包
func (m *Money) OneKeyReward() *cs.LCAllPowerRewardRes {
	powerNum := int32(0)
	for _, reward := range m.powerData.powerRewards {
		powerNum += reward.PowerCount
	}
	result := m.AddMoney(game_def.NickPower, powerNum, 1)
	m.powerData.powerRewards = m.powerData.powerRewards[:0]
	m.RefreshPowerRewardsInRealTime()
	if result {
		return &cs.LCAllPowerRewardRes{
			Result:         0,
			PowerRewardNum: int32(len(m.powerData.powerRewards)),
		}
	} else {
		return &cs.LCAllPowerRewardRes{
			Result: 1,
		}
	}
}

// 单个领取指定体力包 由生成时间标识
func (m *Money) GetPowerRewardByTime(generateTime int64) *cs.LCOnePowerRewardRes {
	result := &cs.LCOnePowerRewardRes{
		LastPowerRewardTime: m.powerData.lastPowerRewardTime,
	}
	if generateTime > time.Now().Unix() {
		result.Result = 1
		for _, reward := range m.powerData.powerRewards {
			if reward != nil {
				result.PowerRewardInfoList = append(result.PowerRewardInfoList, reward)
			}
		}
		return result
	}
	newPowerRewards := make([]*public.PBPowerRewardDataInfo, 0)
	for _, reward := range m.powerData.powerRewards {
		if reward != nil && reward.StartTime == generateTime {
			if m.AddMoney(game_def.NickPower, reward.PowerCount, 1) {
				result.Result = 0
			}
		} else if reward != nil && reward.StartTime != generateTime {
			newPowerRewards = append(newPowerRewards, reward)
		}
	}
	m.powerData.powerRewards = newPowerRewards
	m.RefreshPowerRewardsInRealTime()
	for _, reward := range m.powerData.powerRewards {
		result.PowerRewardInfoList = append(result.PowerRewardInfoList, reward)
	}
	result.LastPowerRewardTime = m.powerData.lastPowerRewardTime
	return result
}
