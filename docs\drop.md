# 道具与掉落系统文档

## 概述

道具与掉落系统是游戏中的核心基础系统，负责管理玩家的道具获取、存储、使用和消耗。系统包含道具管理、背包存储、掉落机制和自选宝箱等功能，为游戏的经济体系和奖励机制提供完整的技术支撑。

## 功能架构

### 核心模块
- **道具模块 (Item)**：道具的统一管理接口，处理道具的增删改查
- **背包模块 (Bag)**：道具的存储容器，管理道具的持久化和同步
- **掉落模块 (Drop)**：掉落逻辑的核心实现，支持权重随机和固定掉落
- **红点模块 (RedDot)**：红点提醒系统，统一管理各功能模块的红点状态

### 数据结构

#### 道具信息 (PBNormalItemInfo)
```protobuf
message PBNormalItemInfo {
    int32 itemId = 2;   // 物品表ID
    int32 value = 3;    // 物品数量
}
```

#### 背包信息 (PBBagItemInfo)
```protobuf
message PBBagItemInfo {
    BagItemType type = 1;               // 背包数据结构类型
    PBNormalItemInfo normalItem = 2;    // 常规物品
}
```

#### 掉落信息 (PBDropItemDataInfo)
```protobuf
message PBDropItemDataInfo {
    int32 itemId = 1;           // 道具ID
    int32 itemCount = 2;        // 道具数量
    AwardItemType itemType = 3; // 道具类型
    bool highEffect = 4;        // 高级特效标识
}
```

#### 道具使用参数 (UseItemParam)
```protobuf
message UseItemParam {
    int32 itemId = 2;                   // 物品模版ID
    int32 useNum = 3;                   // 使用数量
    repeated PBIntPair chooseInfos = 4; // 自选信息集
}
```

#### 数据存储结构
```protobuf
// 背包数据存储
message Bag {
    map<int32, int32> items = 1; // itemId -> count
}

// 红点数据存储
message RedDot {
    map<int32, bool> red_dot_states = 1; // redDotType -> state
}
```

## 配置表结构

### 基础配置表

#### TableItem - 道具基础配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 道具ID |
| NameID | int32 | 道具名称(对应language表) |
| Type | int32 | 道具类型 (ItemType枚举) |
| SubType | int32 | 子类型 |
| Qulity | int32 | 道具品质 |
| Price | int32[] | 道具售价 |
| OptList | int32[] | 道具操作列表 |
| NumParams | int32[] | 道具参数A |
| StrParams | string[] | 道具参数B |
| Stacking | int32 | 最大可叠加数量 |

#### TableDropBox - 掉落包配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 掉落包ID |
| DropOpt | int32 | 掉落规则(固定为0) |
| DropNum | int32 | 每个道具池最大掉落数量 |
| IsCanDuplicate | int32 | 是否可重复(0不可 1可以) |
| DropItem1-10 | string | 道具池配置字符串 |

#### TableDropGroup - 掉落组配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 掉落包ID |
| DropNum | int32 | 最大掉落数量 |
| IsCanDuplicate | int32 | 是否可重复 |
| DropBoxID | int32[] | 掉落道具ID列表 |
| DropWeight | int32[] | 掉落权重列表 |

### 道具类型定义

#### ItemType 枚举
```go
enum ItemType {
    Unavailable = 0;    // 不可使用类
    Drop = 1;           // 掉落类型
    HangUpReward = 2;   // 挂机奖励类型
    ChooseBox = 3;      // 自选宝箱
    Devris = 4;         // 碎片类型
}
```

#### 掉落配置格式
DropItem字段使用JSON字符串格式：
```json
"[[掉落类型，ID，掉落权重，掉落数量基础值，参数1，参数2],[...]]"
```

示例：
```json
"[[1,100,10000,5,0,0],[1,200,5000,10,0,0]]"
```
- 道具100：权重10000，基础数量5
- 道具200：权重5000，基础数量10

## 核心逻辑实现

### 道具管理流程

#### 1. 道具类型判断
```go
func (i *Item) IsMoneyType(itemId int32) bool {
    return i.player.IsMoneyType(itemId)
}
```

#### 2. 道具添加流程
```
配置验证 → 类型判断 → 货币/背包分流 → 数据更新 → 同步客户端 → BI日志
```

#### 3. 道具删除流程
```
参数验证 → 数量检查 → 类型判断 → 货币/背包分流 → 数据更新 → BI日志
```

### 掉落系统算法

#### 1. 权重随机算法
```go
func (d *Drop) DropRandom(baseItemList []*BaseDropItem, num int32, IsCanDuplicate bool) []*BaseDropItem {
    // 1. 构建权重列表
    WeightItemList := make(gameutil.WeightedSlice, 0)
    for _, BaseItem := range baseItemList {
        WeightInfo := gameutil.Weighted{Item: BaseItem, Weight: int(BaseItem.weight)}
        WeightItemList = append(WeightItemList, WeightInfo)
    }
    
    // 2. 执行权重随机选择
    for i := 0; i < int(num); i++ {
        selectedItem, index := WeightItemList.SelectRandom()
        retItemList = append(retItemList, selectedItem.(*BaseDropItem))
        
        // 3. 处理重复逻辑
        if !IsCanDuplicate {
            WeightItemList = append(WeightItemList[:index], WeightItemList[index+1:]...)
        }
    }
    
    return retItemList
}
```

#### 2. 掉落配置解析
```go
func (d *Drop) parseStringToListArray(dropString string) [][]*BaseDropItem {
    // 解析JSON格式的掉落配置
    // "[[1,100,10000,5,0,0],[1,200,5000,10,0,0]]"
    // 转换为BaseDropItem结构体数组
}
```

### 自选宝箱系统

#### 1. 验证流程
```
使用数量检查 → 参数验证 → 配置获取 → 选择验证 → 数量限制检查 → 道具发放
```

#### 2. 核心实现
```go
func (i *Item) handleChooseBoxItem(itemInfo *table_data.TableItem, param *public.UseItemParam) error_code.Code {
    // 1. 单次使用限制
    if param.UseNum != 1 {
        return error_code.ERROR_CHOOSE_BOX_SINGLE_USE
    }
    
    // 2. 获取可选道具列表
    validChoices := i.player.Drop().GetValidChoicesForDropBox(dropId)
    
    // 3. 获取总数量限制
    totalConfigCount := i.player.Drop().GetTotalItemCountForChooseBox(dropId)
    
    // 4. 验证选择并计算总数量
    totalChooseCount := int32(0)
    for _, chooseInfo := range param.ChooseInfos {
        if !validChoices[chooseInfo.Key] {
            return error_code.ERROR_CHOOSE_BOX_INVALID_ITEM
        }
        totalChooseCount += chooseInfo.Value
    }
    
    // 5. 数量限制检查
    if totalChooseCount > totalConfigCount {
        return error_code.ERROR_CHOOSE_BOX_COUNT_EXCEED
    }
    
    // 6. 发放道具
    for _, chooseInfo := range param.ChooseInfos {
        i.AddItem(chooseInfo.Key, chooseInfo.Value, 4)
    }
    
    return error_code.ERROR_OK
}
```

### 背包系统

#### 1. 数据结构管理
```go
type Bag struct {
    player *Player
    db     *dbstruct.Bag
}
```

#### 2. 道具堆叠逻辑
```go
func (b *Bag) AddNormalItemWithoutSync(itemId int32, count int32, causeId int32) bool {
    // 检查背包空间 - 只有当前没有该道具时才检查空间
    if b.db.Items[itemId] == 0 && b.IsBagSpaceFull() {
        return false
    }

    // 直接累加数量
    b.db.Items[itemId] += count
    return true
    b.itemTypeMap[itemId] = append(b.itemTypeMap[itemId], &item)
    
    return true
}
```

### 红点系统

#### 1. 红点状态管理
```go
type RedDot struct {
    player  *Player
    db      *dbstruct.RedDot
    redDots map[public.RedDotType]bool // 红点状态缓存
    dirty   bool                       // 是否需要同步到客户端
}
```

#### 2. 红点更新流程
```go
func (r *RedDot) UpdateRedDot(redDotType public.RedDotType, state bool) {
    // 1. 检查状态是否变化
    if r.redDots[redDotType] == state {
        return
    }
    
    // 2. 更新状态
    r.redDots[redDotType] = state
    r.db.RedDotStates[int32(redDotType)] = state
    r.dirty = true
    
    // 3. 同步到客户端
    r.SyncToClient()
}
```

## 协议流程

### 道具使用协议
```
客户端 → CLUseItemReq → 服务器
服务器 → LCUseItemResp → 客户端
服务器 → LCDropItemListRes → 客户端 (掉落展示)
```

### 背包同步协议
```
服务器 → LCSyncBagItemListRst → 客户端
```

### 红点同步协议
```
服务器 → LCSyncRedDotData → 客户端
```

## 错误码定义

### 道具相关错误码
| 错误码 | 常量名 | 说明 |
|--------|--------|------|
| 1000307 | ERROR_NO_ITEM | 道具不足 |
| 1095035 | ERROR_BAG_FULL | 背包已满 |

### 自选宝箱错误码
| 错误码 | 常量名 | 说明 |
|--------|--------|------|
| 1000308 | ERROR_CHOOSE_BOX_SINGLE_USE | 自选宝箱一次只能使用一个 |
| 1000309 | ERROR_CHOOSE_BOX_INVALID_ITEM | 自选宝箱选择的道具无效 |
| 1000310 | ERROR_CHOOSE_BOX_INVALID_COUNT | 自选宝箱选择的道具数量无效 |
| 1000311 | ERROR_CHOOSE_BOX_COUNT_EXCEED | 自选宝箱选择的道具总数量超过限制 |
| 1000312 | ERROR_CHOOSE_BOX_CONFIG_ERROR | 自选宝箱配置错误 |

## 操作类型定义

### 道具增加类型
```go
ADD_ITEM_DROP_REWARD         // 掉落奖励
ADD_ITEM_CHOOSE_BOX          // 自选宝箱
ADD_ITEM_GM_COMMAND          // GM命令添加
```

### 道具消耗类型
```go
DEL_ITEM_USE_CONSUME         // 道具使用消耗
DEL_ITEM_GM_COMMAND          // GM命令删除
```

## 模块接口设计

### Item模块接口

#### 通用道具接口
```go
// 道具类型判断
func (i *Item) IsMoneyType(itemId int32) bool

// 道具数量检查
func (i *Item) IsCanDelItem(itemId int32, delMoneyNum int32) bool

// 获取道具数量
func (i *Item) GetItemNumById(itemId int32) int32

// 添加道具
func (i *Item) AddItem(itemId int32, addItemNum int32, operateReason int32) bool

// 删除道具
func (i *Item) DelItem(itemId int32, delItemNum int32, operateReason int32) bool

// 掉落专用接口
func (i *Item) DropItemToPlayer(itemId int32, addItemNum int32, operateReason int32) bool
```

#### 道具使用接口
```go
// 道具使用主接口
func (i *Item) UseItem(param *public.UseItemParam) error_code.Code

// 普通掉落道具处理
func (i *Item) handleDropItem(itemInfo *table_data.TableItem, param *public.UseItemParam) error_code.Code

// 自选宝箱处理
func (i *Item) handleChooseBoxItem(itemInfo *table_data.TableItem, param *public.UseItemParam) error_code.Code
```

### Drop模块接口

#### 掉落生成接口
```go
// 根据掉落组ID获取掉落道具
func (d *Drop) GetDropItemsByGroupId(groupId int32) map[int32]int32

// 根据掉落包ID生成掉落道具
func (d *Drop) GenDropItems(dropBoxId int32) map[int32]int32

// 权重随机掉落
func (d *Drop) DropRandom(baseItemList []*BaseDropItem, num int32, IsCanDuplicate bool) []*BaseDropItem
```

#### 自选宝箱支持接口
```go
// 获取自选宝箱的有效选择列表
func (d *Drop) GetValidChoicesForDropBox(dropBoxId int32) map[int32]bool

// 获取自选宝箱的总道具数量
func (d *Drop) GetTotalItemCountForChooseBox(dropBoxId int32) int32
```

#### 配置解析接口
```go
// 解析掉落配置字符串
func (d *Drop) parseStringToListArray(dropString string) [][]*BaseDropItem

// 格式化所有掉落配置
func (d *Drop) FormatAllDropItems()
```

### Bag模块接口

#### 背包管理接口
```go
// 同步背包物品列表
func (b *Bag) SyncBagItemList()

// 添加物品(带同步)
func (b *Bag) AddNormalItem(itemId int32, count int32, causeId int32) bool

// 添加物品(不同步)
func (b *Bag) AddNormalItemWithoutSync(itemId int32, count int32, causeId int32) bool

// 扣除物品
func (b *Bag) DeductNormalItem(itemId int32, count int32, causeId int32) bool

// 获取物品数量
func (b *Bag) GetNormalItemCount(itemId int32) int32

// 检查背包空间
func (b *Bag) IsBagSpaceFull() bool
```

### RedDot模块接口

#### 红点管理接口
```go
// 更新红点状态
func (r *RedDot) UpdateRedDot(redDotType public.RedDotType, state bool)

// 获取红点状态
func (r *RedDot) GetRedDotState(redDotType public.RedDotType) bool

// 同步红点到客户端
func (r *RedDot) SyncToClient()

// 刷新所有红点
func (r *RedDot) RefreshAllRedDots()
```

## 使用示例

### 基础道具操作示例

#### 1. 添加道具
```go
// 添加100个道具ID为1001的道具，操作原因为4(掉落奖励)
success := player.Item().AddItem(1001, 100, 4)
if !success {
    log.Error("添加道具失败")
}
```

#### 2. 检查和扣除道具
```go
// 检查是否有足够的道具
if player.Item().IsCanDelItem(1001, 50) {
    // 扣除50个道具
    success := player.Item().DelItem(1001, 50, 5)
    if !success {
        log.Error("扣除道具失败")
    }
}
```

#### 3. 获取道具数量
```go
count := player.Item().GetItemNumById(1001)
log.Info("当前道具数量", log.Kv("itemId", 1001), log.Kv("count", count))
```

### 掉落系统使用示例

#### 1. 执行掉落组掉落
```go
// 执行掉落组ID为100的掉落，操作原因为4，掉落类型为1
errorCode := player.DropToPlayer(100, 4, 1)
if errorCode != error_code.ERROR_OK {
    log.Error("掉落失败", log.Kv("errorCode", errorCode))
}
```

#### 2. 直接使用掉落包
```go
// 获取掉落包ID为200的掉落道具
dropItems := player.Drop().GenDropItems(200)
for itemId, count := range dropItems {
    player.Item().AddItem(itemId, count, 4)
}
```

### 自选宝箱使用示例

#### 1. 客户端请求使用自选宝箱
```go
param := &public.UseItemParam{
    ItemId: 3001,  // 自选宝箱道具ID
    UseNum: 1,     // 只能使用1个
    ChooseInfos: []*public.PBIntPair{
        {Key: 1001, Value: 5},   // 选择道具1001的5个
        {Key: 1002, Value: 10},  // 选择道具1002的10个
    },
}

errorCode := player.Item().UseItem(param)
if errorCode != error_code.ERROR_OK {
    // 处理错误
}
```

#### 2. 获取自选宝箱可选道具
```go
validChoices := player.Drop().GetValidChoicesForDropBox(dropBoxId)
for itemId := range validChoices {
    log.Info("可选道具", log.Kv("itemId", itemId))
}
```

### 红点系统使用示例

#### 1. 更新红点状态
```go
// 设置背包红点为true
player.RedDot().UpdateRedDot(public.RedDotType_Bag, true)

// 清除任务红点
player.RedDot().UpdateRedDot(public.RedDotType_Mission, false)
```

#### 2. 检查红点状态
```go
hasBagRedDot := player.RedDot().GetRedDotState(public.RedDotType_Bag)
if hasBagRedDot {
    log.Info("背包有红点提醒")
}
```

## 配置示例

### TableItem配置示例
```json
{
    "ID": 3001,
    "NameID": 30001,
    "Type": 3,
    "SubType": 0,
    "Qulity": 3,
    "NumParams": [100],
    "OptList": [1],
    "Stacking": 999
}
```

### TableDropBox配置示例
```json
{
    "ID": 100,
    "DropOpt": 0,
    "DropNum": 1,
    "IsCanDuplicate": 0,
    "DropItem1": "[[1,1001,5000,5,0,0],[1,1002,3000,10,0,0],[1,1003,2000,3,0,0]]"
}
```

### TableDropGroup配置示例
```json
{
    "ID": 200,
    "DropNum": 2,
    "IsCanDuplicate": 1,
    "DropBoxID": [100, 101, 102],
    "DropWeight": [5000, 3000, 2000]
}
```

## 性能优化

### 1. 掉落配置缓存
- 在Drop模块初始化时解析所有掉落配置
- 使用map[int32]*ItemsDrop缓存解析结果
- 避免每次掉落时重复解析配置字符串

### 2. 背包存储优化
- 使用map[int32]int32直接存储itemId到数量的映射
- 简化数据结构，提高访问效率
- 减少内存占用和复杂的索引维护

### 3. 红点状态缓存
- 使用内存缓存红点状态，避免频繁数据库查询
- 批量同步机制，减少网络通信次数
- 脏标记机制，只在状态变化时同步

### 4. 批量操作支持
```go
// 批量添加道具，减少同步次数
func (p *Player) AddBagItemGroup(itemGroup map[int32]int32, operateReason int32) bool {
    for itemId, count := range itemGroup {
        if !p.bag.AddNormalItemWithoutSync(itemId, count, operateReason) {
            return false
        }
    }
    p.Bag().SyncBagItemList()  // 统一同步
    return true
}
```

## 测试建议

### 功能测试
1. **基础功能**：道具增删改查、背包存储、掉落生成
2. **边界测试**：背包满、道具不足、配置缺失
3. **业务逻辑**：自选宝箱验证、红点状态管理
4. **错误处理**：参数验证、异常情况处理

### 性能测试
1. **掉落性能**：大量掉落操作的性能表现
2. **背包容量**：背包满载情况下的操作性能
3. **并发测试**：多玩家同时操作的性能表现
4. **内存使用**：长时间运行的内存占用情况

### 数据一致性测试
1. **数据同步**：客户端与服务器数据一致性
2. **事务完整性**：操作失败时的数据回滚
3. **跨模块交互**：与其他系统的数据交换正确性

## 注意事项

### 开发注意事项
1. **配置表依赖**：确保所有相关配置表正确配置和加载
2. **数据类型**：注意int32和int64的使用场景
3. **错误处理**：完善的参数验证和错误码返回
4. **日志记录**：关键操作的详细日志记录

### 运营注意事项
1. **数据统计**：建议添加道具流水的BI统计
2. **配置热更**：支持配置表的热更新机制
3. **数据备份**：重要道具数据的备份策略

### 扩展性注意事项
1. **模块解耦**：保持各模块间的低耦合设计
2. **接口稳定**：对外接口的向后兼容性
3. **配置扩展**：预留配置字段用于功能扩展

---

*本文档基于当前代码实现编写，如有代码变更请及时更新文档内容。*
