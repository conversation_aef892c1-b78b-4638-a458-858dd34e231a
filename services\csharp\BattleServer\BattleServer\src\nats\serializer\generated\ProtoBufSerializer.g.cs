// <auto-generated />
// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: GameService.proto

using System.Buffers;
using BattleServer.Service;

namespace BattleServer.Nats.SerializerRegistry;

public partial class ProtoBufSerializer<T>
{
	private static readonly Dictionary<Type, Func<ReadOnlySequence<byte>, T>> Parsers = new()
	{
		{ typeof(BattleEndReq), (buffer) => (T)(object)BattleEndReq.Parser.ParseFrom(buffer) },
		{ typeof(MatchResultRequest), (buffer) => (T)(object)MatchResultRequest.Parser.ParseFrom(buffer) },
		{ typeof(AuthResp), (buffer) => (T)(object)AuthResp.Parser.ParseFrom(buffer) },
		{ typeof(RoundStartReq), (buffer) => (T)(object)RoundStartReq.Parser.ParseFrom(buffer) },
		{ typeof(RoundBattleStartReq), (buffer) => (T)(object)RoundBattleStartReq.Parser.ParseFrom(buffer) },
		{ typeof(BattleStateChangeResp), (buffer) => (T)(object)BattleStateChangeResp.Parser.ParseFrom(buffer) },
		{ typeof(AuthReq), (buffer) => (T)(object)AuthReq.Parser.ParseFrom(buffer) },
		{ typeof(RoundBattleEndResp), (buffer) => (T)(object)RoundBattleEndResp.Parser.ParseFrom(buffer) },
		{ typeof(MatchResultResponse), (buffer) => (T)(object)MatchResultResponse.Parser.ParseFrom(buffer) },
		{ typeof(RoundBattleStartResp), (buffer) => (T)(object)RoundBattleStartResp.Parser.ParseFrom(buffer) },
		{ typeof(RoundBattleEndReq), (buffer) => (T)(object)RoundBattleEndReq.Parser.ParseFrom(buffer) },
		{ typeof(BattleEndResp), (buffer) => (T)(object)BattleEndResp.Parser.ParseFrom(buffer) },
		{ typeof(BattleStateChangeReq), (buffer) => (T)(object)BattleStateChangeReq.Parser.ParseFrom(buffer) },
		{ typeof(RoundStartResp), (buffer) => (T)(object)RoundStartResp.Parser.ParseFrom(buffer) },
	};
}
