package player

import (
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/pkg/log"
)

// Bag 背包模块
type Bag struct {
	player *Player
	db     *dbstruct.Bag
}

func NewBag(p *Player) *Bag {
	return &Bag{
		player: p,
	}
}

// InitDB 初始化模块数据
func (b *Bag) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Bag == nil {
		db.Game.Bag = &dbstruct.Bag{
			Items: make(map[int32]int32),
		}
	}

	b.db = db.Game.Bag
	if b.db.Items == nil {
		log.Warn("Bag data structure not properly initialized InitDB", log.Kv("uid", b.player.Uid()))
		b.db.Items = make(map[int32]int32)
	}

	log.Info("Bag InitDB")
}

// OnCrossDay 处理跨天逻辑
func (b *Bag) OnCrossDay(natural bool, nowUnix int64) {
	// 处理跨天逻辑，如清理过期道具等
	// TODO: 实现跨天逻辑
}

// SyncBagItemList 同步背包物品列表
// @Export
func (b *Bag) SyncBagItemList() {
	// 1. 构建同步消息
	resp := &cs.LCSyncBagItemListRst{
		BagItemList: b.buildPBBagItemList(),
	}

	// 2. 发送同步消息给客户端
	b.player.SendToClient(rpc_def.LCSyncBagItemListRst, resp, false)

	log.Info("SyncBagItemList")
}

// buildPBBagItemList 构建背包物品列表
func (b *Bag) buildPBBagItemList() []*public.PBBagItemInfo {
	bagItems := make([]*public.PBBagItemInfo, 0, len(b.db.Items))

	for itemId, count := range b.db.Items {
		if count > 0 {
			bagItems = append(bagItems, &public.PBBagItemInfo{
				NormalItem: &public.PBNormalItemInfo{
					ItemId: itemId,
					Value:  count,
				},
			})
			log.Info("buildPBBagItemList", log.Kv("Uid", b.player.Uid()), log.Kv("ItemId", itemId), log.Kv("Value", count))
		}
	}

	return bagItems
}

// AddNormalItem 添加物品
// @Export
func (b *Bag) AddNormalItem(itemId int32, count int32, causeId int32) bool {
	reBool := b.AddNormalItemWithoutSync(itemId, count, causeId)
	b.SyncBagItemList()

	return reBool
}

// AddNormalItemWithoutSync 添加物品
// @Export
func (b *Bag) AddNormalItemWithoutSync(itemId int32, count int32, causeId int32) bool {
	// 检查背包空间 - 只有当前没有该道具时才检查空间
	if b.db.Items[itemId] == 0 && b.IsBagSpaceFull() {
		log.Warn("AddNormalItem lack of bag space")
		return false
	}

	if b.db.Items == nil {
		log.Warn("Bag data structure not properly initialized", log.Kv("uid", b.player.Uid()))
		b.db.Items = make(map[int32]int32)
	}

	// 直接累加数量
	b.db.Items[itemId] += count

	log.Info("AddNormalItem success",
		log.Kv("itemId", itemId),
		log.Kv("count", count),
		log.Kv("causeId", causeId),
		log.Kv("totalValue", b.db.Items[itemId]))
	return true
}

// DeductNormalItem 扣除物品
// @Export
func (b *Bag) DeductNormalItem(itemId int32, count int32, operateReason int32) bool {
	currentCount := b.db.Items[itemId]
	if currentCount == 0 {
		log.Warn("DeductNormalItem no item found",
			log.Kv("itemId", itemId))
		return false
	}

	// 如果count为0，表示删除所有
	if count == 0 {
		count = currentCount
	}

	if currentCount < count {
		log.Warn("DeductNormalItem not enough items",
			log.Kv("itemId", itemId),
			log.Kv("required", count),
			log.Kv("current", currentCount))
		return false
	}

	// 扣除数量
	b.db.Items[itemId] -= count

	// 如果数量为0，从map中删除
	if b.db.Items[itemId] == 0 {
		delete(b.db.Items, itemId)
	}

	// 同步到客户端
	b.SyncBagItemList()

	log.Info("DeductNormalItem success",
		log.Kv("itemId", itemId),
		log.Kv("count", count),
		log.Kv("remaining", b.db.Items[itemId]))
	return true
}

// ClearItemsByID 清理指定ID的物品
// @Export
func (b *Bag) ClearItemsByID(itemId int32) {
	clearedCount := b.db.Items[itemId]
	if clearedCount == 0 {
		return
	}

	// 从map中删除
	delete(b.db.Items, itemId)

	// 同步到客户端
	b.SyncBagItemList()

	log.Info("ClearItemsByID success",
		log.Kv("itemId", itemId),
		log.Kv("clearedCount", clearedCount))
}

// clearAllNormalItems 清理所有普通物品
func (b *Bag) clearAllNormalItems() {
	b.db.Items = make(map[int32]int32)

	// 同步到客户端
	b.SyncBagItemList()
}

// CheckNormalItemCount 检查物品数量是否足够
// @Export
func (b *Bag) CheckNormalItemCount(itemId int32, count int32) bool {
	return b.db.Items[itemId] >= count
}

// GetNormalItemCount 获取物品数量
// @Export
func (b *Bag) GetNormalItemCount(itemId int32) int32 {
	return b.db.Items[itemId]
}

// IsBagSpaceFull 背包空间是否已满
func (b *Bag) IsBagSpaceFull() bool {
	return b.getBagOccupyNum() >= game_def.PlayerBagSize
}

// GetBagOccupyNum 获取背包已被占用的格子数
func (b *Bag) getBagOccupyNum() int {
	return len(b.db.Items)
}
